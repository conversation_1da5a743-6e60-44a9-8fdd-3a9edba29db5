#!/usr/bin/env python3
"""
Script genérico para ejecutar reportes ETL configurables.
<PERSON> datos desde S3, los procesa mediante consultas SQL y los exporta a archivos Parquet.

Uso:
    python run_reporte.py NOMBRE_REPORTE

Ejemplo:
    python run_reporte.py REPORTE_FULLCARGA
"""
import os
import sys
import configparser
import logging
import duckdb
import boto3
import shutil
import subprocess
from datetime import datetime, timedelta

def main():
    # Verificar argumentos
    if len(sys.argv) != 2:
        print("Uso: python run_reporte.py NOMBRE_REPORTE")
        print("Ejemplo: python run_reporte.py REPORTE_FULLCARGA")
        sys.exit(1)

    # Obtener nombre del reporte
    nombre_reporte = sys.argv[1]

    # Construir rutas
    config_path = os.path.join("CONFIGURACIONES", nombre_reporte, "config.ini")
    queries_dir = os.path.join("REPORTES", nombre_reporte, "queries")

    # Verificar que existan los directorios y archivos necesarios
    if not os.path.exists(config_path):
        print(f"Error: No se encontró el archivo de configuración en {config_path}")
        sys.exit(1)

    if not os.path.exists(queries_dir):
        print(f"Error: No se encontró el directorio de queries en {queries_dir}")
        sys.exit(1)

    # Leer configuración
    config = configparser.ConfigParser()
    config.read(config_path)

    # Configurar logging
    log_file = setup_logging(nombre_reporte, config)

    # Determinar rango de fechas
    fecha_inicio, fecha_fin = get_date_range(config)
    logging.info(f"Procesando datos para {nombre_reporte} desde {fecha_inicio} hasta {fecha_fin}")

    # Procesar cada fecha en el rango
    current_date = fecha_inicio
    while current_date <= fecha_fin:
        fecha_str = current_date.strftime('%Y-%m-%d')
        process_date(fecha_str, config, queries_dir, nombre_reporte)
        current_date += timedelta(days=1)

    logging.info("Proceso completado")

def setup_logging(nombre_reporte, config):
    """Configura el sistema de logging"""
    # Crear directorio de logs si no existe
    log_dir = config.get('general', 'log_dir', fallback='logs')
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)

    # Configurar el formato del log
    log_format = '%(asctime)s - %(levelname)s - %(message)s'

    # Crear un manejador para el archivo de log
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = f'{log_dir}/{nombre_reporte}_{timestamp}.log'

    # Configurar el logger
    logging.basicConfig(
        level=logging.INFO,
        format=log_format,
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()  # También mostrar en consola
        ]
    )

    return log_file

def get_date_range(config):
    """Determina el rango de fechas a procesar según la configuración"""
    # Leer el valor del switch de rango
    usar_rango_fijo = config.getboolean('fecha', 'rango', fallback=False)

    if usar_rango_fijo:
        # Usar rango fijo
        return (
            datetime.strptime(config.get('fecha', 'rango_inicio'), '%Y-%m-%d').date(),
            datetime.strptime(config.get('fecha', 'rango_fin'), '%Y-%m-%d').date()
        )
    else:
        # Usar modo relativo
        dias_atras = int(config.get('fecha', 'dias_atras', fallback='1'))
        today = datetime.now().date()
        return (today - timedelta(days=dias_atras), today - timedelta(days=1))

def run_post_processing_scripts(config, fecha, output_file, output_dir):
    """Ejecuta los scripts de post-procesamiento configurados"""
    # Verificar si existe la sección post_processing
    if not config.has_section('post_processing'):
        logging.info("No se encontró la sección [post_processing] en el archivo de configuración. No se ejecutarán scripts de post-procesamiento.")
        return

    # Verificar si hay scripts configurados
    if not config.has_option('post_processing', 'scripts'):
        logging.info("No hay scripts de post-procesamiento configurados.")
        return

    # Obtener la lista de scripts
    scripts_config = config.get('post_processing', 'scripts', fallback='')
    if not scripts_config.strip():
        logging.info("No hay scripts de post-procesamiento configurados.")
        return

    # Procesar cada script
    scripts = [s.strip() for s in scripts_config.split(',')]
    for script_path in scripts:
        if not script_path:
            continue

        # Construir el comando
        command = [sys.executable, script_path, fecha]

        # Ejecutar el script
        logging.info(f"Ejecutando script de post-procesamiento: {script_path} {fecha}")
        try:
            result = subprocess.run(command, capture_output=True, text=True)
            if result.returncode == 0:
                logging.info(f"Script de post-procesamiento ejecutado con éxito: {script_path}")
                if result.stdout:
                    logging.info(f"Salida del script:\n{result.stdout}")
            else:
                logging.error(f"Error al ejecutar el script de post-procesamiento: {script_path}")
                if result.stderr:
                    logging.error(f"Error: {result.stderr}")
                if result.stdout:
                    logging.info(f"Salida: {result.stdout}")
        except Exception as e:
            logging.error(f"Error al ejecutar el script de post-procesamiento {script_path}: {str(e)}")

def process_date(fecha, config, queries_dir, nombre_reporte):
    """Procesa los datos para una fecha específica"""
    logging.info(f"Procesando fecha: {fecha}")

    # Extraer componentes de la fecha
    fecha_parts = fecha.split('-')
    year, month, day = fecha_parts[0], fecha_parts[1], fecha_parts[2]

    # Obtener hora actual para el nombre del archivo
    hora_actual = datetime.now().strftime('%H%M%S')

    # Configurar conexión a DuckDB
    conn = duckdb.connect(database=':memory:')

    # Obtener credenciales activas de AWS CLI
    session = boto3.Session()
    credentials = session.get_credentials().get_frozen_credentials()

    # Cargar soporte para S3 en DuckDB
    conn.sql("INSTALL httpfs;")
    conn.sql("LOAD httpfs;")
    conn.sql("SET s3_region='us-east-1';")
    conn.sql("SET s3_use_ssl=true;")
    conn.sql("SET s3_url_style='path';")

    # Pasar credenciales explícitamente
    conn.sql(f"SET s3_access_key_id='{credentials.access_key}';")
    conn.sql(f"SET s3_secret_access_key='{credentials.secret_key}';")
    conn.sql(f"SET s3_session_token='{credentials.token}';")

    # Construir las rutas S3 para todos los orígenes definidos
    s3_paths = {}

    # Verificar que exista la sección s3_sources
    if not config.has_section('s3_sources'):
        logging.error("Error: No se encontró la sección [s3_sources] en el archivo de configuración")
        raise ValueError("La sección [s3_sources] es obligatoria en el archivo de configuración")

    # Procesar cada fuente definida en la sección s3_sources
    for source_name, source_value in config.items('s3_sources'):
        parts = [p.strip() for p in source_value.split(',')]
        if len(parts) >= 2:
            bucket = parts[0]
            prefix = parts[1]
            region = parts[2] if len(parts) > 2 else 'us-east-1'

            s3_paths[source_name] = {
                'bucket': bucket,
                'prefix': prefix,
                'region': region,
                'path': f's3://{bucket}/{prefix}/{year}/{month}/{day}/*'
            }

    # Construir la ruta de destino para el archivo Parquet
    output_base_dir = config.get('general', 'output_dir')
    output_dir = f'{output_base_dir}/{year}/{month}/{day}'
    output_file = f'{output_dir}/{nombre_reporte}_{fecha}_{hora_actual}.parquet'

    # No crear la carpeta hasta que sepamos que hay registros para procesar

    try:
        # Obtener lista de queries a ejecutar
        query_list = config.get('queries', 'query_list', fallback='').split(',')
        query_list = [q.strip() for q in query_list if q.strip()]

        if not query_list:
            logging.error("No se especificaron queries para ejecutar")
            return

        # Procesar cada query
        for query_name in query_list:
            query_file = os.path.join(queries_dir, f"{query_name}.sql")

            if not os.path.exists(query_file):
                logging.error(f"No se encontró el archivo de query: {query_file}")
                continue

            # Leer y parametrizar la query
            with open(query_file, 'r') as f:
                query_template = f.read()

            # Calcular el día siguiente para buscar transacciones reversadas
            fecha_dt = datetime.strptime(fecha, '%Y-%m-%d')
            next_date = fecha_dt + timedelta(days=1)
            next_year, next_month, next_day = next_date.strftime('%Y'), next_date.strftime('%m'), next_date.strftime('%d')

            # Crear un diccionario con todos los parámetros para formatear la query
            format_params = {
                'year': year,
                'month': month,
                'day': day,
                'next_year': next_year,
                'next_month': next_month,
                'next_day': next_day,
                'fecha_inicio': fecha,
                'fecha_fin': fecha
            }

            # TODO EL ERSTO DE LA TABLAS {nombre_fuente}_bucket
            # Agregar todos los paths S3 como parámetros
            # Para cada fuente definida en config.ini, generamos variables como:
            # {nombre_fuente}_bucket, {nombre_fuente}_prefix, {nombre_fuente}_path, etc.
            # Estas variables pueden usarse en los archivos SQL
            for source_name, source_info in s3_paths.items():
                format_params[f'{source_name}_bucket'] = source_info['bucket']
                format_params[f'{source_name}_prefix'] = source_info['prefix']
                format_params[f'{source_name}_region'] = source_info['region']
                format_params[f'{source_name}_path'] = source_info['path']

                # Ejemplo: Si source_name es 'cashin_prod', se generan:
                # - cashin_prod_bucket
                # - cashin_prod_prefix
                # - cashin_prod_region
                # - cashin_prod_path (ruta completa)

            # Reemplazar parámetros en la query
            # Primero, escapar las llaves que no son parte del formato
            query_template = query_template.replace('{{', '{{{{').replace('}}', '}}}}')
            
            # Debug: mostrar variables disponibles
            logging.info(f"Variables disponibles para reemplazo: {list(format_params.keys())}")
            
            query = query_template.format(**format_params)

            # Ejecutar la query
            logging.info(f"Ejecutando query: {query_name}")
            try:
                # Intentar ejecutar la query original primero
                df = conn.sql(query).fetchdf()
                logging.info(f"Se encontraron {len(df)} registros para procesar")
            except Exception as query_error:
                error_str = str(query_error)
                logging.warning(f"Error al ejecutar la query original: {error_str}")

                # Aplicar una solución que preserve todos los datos tratándolos como texto
                logging.info("Aplicando solución para manejar JSON como texto (VARCHAR)...")

                # Modificar la query para tratar los campos JSON como texto
                query_modified = query

                # Modificar la sección USER_MODIFICATION_DAY para tratar OLD_DATA y NEW_DATA como texto
                if "WHEN TRY_CAST(UMH.OLD_DATA AS JSON) IS NOT NULL THEN UMH.OLD_DATA" in query_modified:
                    query_modified = query_modified.replace(
                        "CASE\n            WHEN TRY_CAST(UMH.OLD_DATA AS JSON) IS NOT NULL THEN UMH.OLD_DATA\n            ELSE NULL\n        END AS OLD_DATA",
                        "UMH.OLD_DATA AS OLD_DATA"
                    )

                if "WHEN TRY_CAST(UMH.NEW_DATA AS JSON) IS NOT NULL THEN UMH.NEW_DATA" in query_modified:
                    query_modified = query_modified.replace(
                        "CASE\n            WHEN TRY_CAST(UMH.NEW_DATA AS JSON) IS NOT NULL THEN UMH.NEW_DATA\n            ELSE NULL\n        END AS NEW_DATA",
                        "UMH.NEW_DATA AS NEW_DATA"
                    )

                # Modificar la sección final para tratar oldData y newData como texto
                if "WHEN TRY_CAST(P.oldData AS JSON) IS NOT NULL THEN P.oldData" in query_modified:
                    query_modified = query_modified.replace(
                        "CASE\n        WHEN TRY_CAST(P.oldData AS JSON) IS NOT NULL THEN P.oldData\n        ELSE NULL\n    END AS oldData",
                        "P.oldData AS oldData"
                    )

                if "WHEN TRY_CAST(P.newData AS JSON) IS NOT NULL THEN P.newData" in query_modified:
                    query_modified = query_modified.replace(
                        "CASE\n        WHEN TRY_CAST(P.newData AS JSON) IS NOT NULL THEN P.newData\n        ELSE NULL\n    END AS newData",
                        "P.newData AS newData"
                    )

                # Modificar la sección que extrae RAZON para usar CAST en lugar de TRY_CAST
                if "WHEN TRY_CAST(UMH.NEW_DATA AS JSON) IS NOT NULL AND TRY_CAST(JSON_EXTRACT" in query_modified:
                    # Buscar la sección CASE completa para RAZON
                    razon_case_start = query_modified.find("CASE\n            -- Solo intentar extraer JSON si el campo es un JSON válido")
                    if razon_case_start >= 0:
                        razon_case_end = query_modified.find("END AS RAZON", razon_case_start)
                        if razon_case_end >= 0:
                            razon_case_end += len("END AS RAZON")
                            razon_case = query_modified[razon_case_start:razon_case_end]

                            # Crear una nueva sección CASE para RAZON que maneje el caso especial
                            new_razon_case = """CASE
            -- Intentar extraer RAZON de manera segura
            WHEN UMH.NEW_DATA = 'CIERRE POR APP BIM' THEN 'CIERRE POR APP BIM'
            WHEN UMH.NEW_DATA LIKE '%CIERRE POR APP BIM%' THEN 'CIERRE POR APP BIM'
            WHEN TRY_CAST(UMH.NEW_DATA AS JSON) IS NOT NULL THEN
                COALESCE(
                    TRY_CAST(JSON_EXTRACT(UMH.NEW_DATA, '$.profileDetails.remarks') AS VARCHAR),
                    TRY_CAST(JSON_EXTRACT(UMH.NEW_DATA, '$.profileDetails[0].remarks') AS VARCHAR)
                )
            ELSE NULL
        END AS RAZON"""

                            # Reemplazar la sección CASE original con la nueva
                            query_modified = query_modified.replace(razon_case, new_razon_case)

                # Si hay problemas con LIKE en JSON, convertir a VARCHAR
                if "WHEN UMH.NEW_DATA LIKE '%CIERRE POR APP BIM%'" in query_modified:
                    query_modified = query_modified.replace(
                        "WHEN UMH.NEW_DATA LIKE '%CIERRE POR APP BIM%'",
                        "WHEN CAST(UMH.NEW_DATA AS VARCHAR) LIKE '%CIERRE POR APP BIM%'"
                    )

                try:
                    df = conn.sql(query_modified).fetchdf()
                    logging.info(f"Solución para tratar JSON como texto aplicada con éxito. Se encontraron {len(df)} registros para procesar")
                except Exception as e:
                    logging.error(f"Error al aplicar la solución para tratar JSON como texto: {str(e)}")

                    # Si la solución anterior falla, intentar una solución más simple
                    logging.warning("Intentando solución alternativa...")

                    # Modificar la query de manera más simple
                    query_simple = query

                    # Reemplazar todas las validaciones de JSON con conversiones directas a VARCHAR
                    query_simple = query_simple.replace("TRY_CAST(UMH.OLD_DATA AS JSON)", "CAST(UMH.OLD_DATA AS VARCHAR)")
                    query_simple = query_simple.replace("TRY_CAST(UMH.NEW_DATA AS JSON)", "CAST(UMH.NEW_DATA AS VARCHAR)")
                    query_simple = query_simple.replace("TRY_CAST(P.oldData AS JSON)", "CAST(P.oldData AS VARCHAR)")
                    query_simple = query_simple.replace("TRY_CAST(P.newData AS JSON)", "CAST(P.newData AS VARCHAR)")

                    # Reemplazar JSON_EXTRACT con NULL para evitar errores
                    query_simple = query_simple.replace("JSON_EXTRACT(UMH.NEW_DATA, '$.profileDetails.remarks')", "NULL")
                    query_simple = query_simple.replace("JSON_EXTRACT(UMH.NEW_DATA, '$.profileDetails[0].remarks')", "NULL")

                    try:
                        df = conn.sql(query_simple).fetchdf()
                        logging.info(f"Solución alternativa aplicada con éxito. Se encontraron {len(df)} registros para procesar")
                    except Exception as e2:
                        logging.error(f"Error al aplicar la solución alternativa: {str(e2)}")

                        # Si todo lo demás falla, usar la solución de emergencia original
                        logging.warning("Aplicando solución de emergencia (reemplazar operaciones JSON con NULL)...")

                        # Modificar la query para evitar problemas de JSON
                        query_emergency = query

                        # Reemplazar todas las operaciones TRY_CAST con NULL
                        query_emergency = query_emergency.replace("TRY_CAST(P.newData AS JSON)", "NULL")
                        query_emergency = query_emergency.replace("TRY_CAST(P.oldData AS JSON)", "NULL")
                        query_emergency = query_emergency.replace("TRY_CAST(UMH.NEW_DATA AS JSON)", "NULL")
                        query_emergency = query_emergency.replace("TRY_CAST(UMH.OLD_DATA AS JSON)", "NULL")

                        # Reemplazar JSON_EXTRACT con NULL
                        query_emergency = query_emergency.replace("JSON_EXTRACT(UMH.NEW_DATA, '$.profileDetails.remarks')", "NULL")
                        query_emergency = query_emergency.replace("JSON_EXTRACT(UMH.NEW_DATA, '$.profileDetails[0].remarks')", "NULL")

                        try:
                            df = conn.sql(query_emergency).fetchdf()
                            logging.info(f"Solución de emergencia aplicada. Se encontraron {len(df)} registros para procesar")
                        except Exception as emergency_error:
                            logging.error(f"Error al aplicar solución de emergencia: {str(emergency_error)}")
                            raise

            if len(df) == 0:
                logging.warning(f"No hay registros para procesar en la fecha {fecha}")
                continue

            # Si hay registros, verificar si existe la carpeta del día y eliminarla para evitar duplicados
            if os.path.exists(output_dir):
                logging.info(f"Eliminando carpeta existente para evitar duplicados: {output_dir}")
                shutil.rmtree(output_dir)

            # Crear directorios si no existen
            os.makedirs(output_dir, exist_ok=True)

            # Exportar a Parquet
            conn.sql(f"""
                CREATE TABLE temp_data AS SELECT * FROM df
            """)

            conn.sql(f"""
                COPY (SELECT * FROM temp_data)
                TO '{output_file}' (FORMAT PARQUET)
            """)

            logging.info(f"Resultados exportados a {output_file}")

            # Mostrar muestra de datos
            muestra_df = conn.sql("SELECT * FROM temp_data LIMIT 5").fetchdf()
            logging.info(f"Muestra de datos procesados:\n{muestra_df}")

            # Limpiar tabla temporal
            conn.sql("DROP TABLE IF EXISTS temp_data")

            # Ejecutar scripts de post-procesamiento si están configurados
            run_post_processing_scripts(config, fecha, output_file, output_dir)

    except Exception as e:
        logging.error(f"Error al procesar la fecha {fecha}: {str(e)}")

if __name__ == "__main__":
    main()
