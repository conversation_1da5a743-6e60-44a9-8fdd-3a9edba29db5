2025-06-08 22:35:40,773 - INFO - <PERSON><PERSON><PERSON><PERSON> datos para REPORTE_LOG_USUARIO desde 2025-05-05 hasta 2025-05-05
2025-06-08 22:35:40,773 - INFO - Procesando fecha: 2025-05-05
2025-06-08 22:35:40,797 - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-06-08 22:35:40,851 - INFO - Variables disponibles para reemplazo: ['year', 'month', 'day', 'next_year', 'next_month', 'next_day', 'fecha_inicio', 'fecha_fin', 'user_profile_bucket', 'user_profile_prefix', 'user_profile_region', 'user_profile_path', 'user_identifier_bucket', 'user_identifier_prefix', 'user_identifier_region', 'user_identifier_path', 'kyc_details_bucket', 'kyc_details_prefix', 'kyc_details_region', 'kyc_details_path', 'issuer_details_bucket', 'issuer_details_prefix', 'issuer_details_region', 'issuer_details_path', 'mtx_categories_bucket', 'mtx_categories_prefix', 'mtx_categories_region', 'mtx_categories_path', 'channel_grades_bucket', 'channel_grades_prefix', 'channel_grades_region', 'channel_grades_path', 'mtx_wallet_bucket', 'mtx_wallet_prefix', 'mtx_wallet_region', 'mtx_wallet_path', 'user_modification_history_bucket', 'user_modification_history_prefix', 'user_modification_history_region', 'user_modification_history_path', 'user_auth_change_history_bucket', 'user_auth_change_history_prefix', 'user_auth_change_history_region', 'user_auth_change_history_path', 'user_account_history_bucket', 'user_account_history_prefix', 'user_account_history_region', 'user_account_history_path']
2025-06-08 22:35:40,851 - INFO - Ejecutando query: log_usuarios_all
2025-06-08 22:35:43,602 - INFO - Se encontraron 1000 registros para procesar
2025-06-08 22:35:43,602 - INFO - Eliminando carpeta existente para evitar duplicados: REPORTE_LOG_USUARIOS/2025/05/05
2025-06-08 22:35:43,619 - INFO - Resultados exportados a REPORTE_LOG_USUARIOS/2025/05/05/REPORTE_LOG_USUARIO_2025-05-05_223540.parquet
2025-06-08 22:35:43,624 - INFO - Muestra de datos procesados:
               USER_ID          CREATED_ON        TIPO_EVENTO                                           OLD_DATA                                           NEW_DATA CREATED_BY
0  US.**************** 2025-05-05 23:57:06  USER_MODIFICATION  {"notificationEndpointRequests":[{"notificatio...  {"notificationEndpointRequests":[{"notificatio...       None
1  US.**************** 2025-05-05 23:57:05  USER_MODIFICATION  {"profileDetails":[{"identifierValue":"5199718...  {"profileDetails":[{"identifierValue":"5199718...       None
2   US.557396933695614 2025-05-05 23:44:02  USER_MODIFICATION  {"notificationEndpointRequests":[{"notificatio...  {"notificationEndpointRequests":[{"notificatio...       None
3   US.557396933695614 2025-05-05 23:44:01  USER_MODIFICATION  {"profileDetails":[{"identifierValue":"5195329...  {"profileDetails":[{"identifierValue":"5195329...       None
4  US.2252431192200454 2025-05-05 23:40:27  USER_MODIFICATION  {"notificationEndpointRequests":[{"notificatio...  {"notificationEndpointRequests":[{"notificatio...       None
2025-06-08 22:35:43,625 - INFO - Ejecutando script de post-procesamiento: REPORTES/REPORTE_LOG_USUARIO/preprocesar_log_usuario.py 2025-05-05
2025-06-08 22:35:43,652 - INFO - Script de post-procesamiento ejecutado con éxito: REPORTES/REPORTE_LOG_USUARIO/preprocesar_log_usuario.py
2025-06-08 22:35:43,652 - INFO - Salida del script:
Preprocesamiento para LOG_USUARIO en fecha: 2025-05-05

2025-06-08 22:35:43,652 - INFO - Ejecutando script de post-procesamiento: REPORTES/REPORTE_LOG_USUARIO/procesar_log_usuarios.py 2025-05-05
2025-06-08 22:35:43,677 - INFO - Script de post-procesamiento ejecutado con éxito: REPORTES/REPORTE_LOG_USUARIO/procesar_log_usuarios.py
2025-06-08 22:35:43,678 - INFO - Salida del script:
Procesando LOG_USUARIO: 2025-05-05 para la fecha 2025-06-08 en output/csv_exports

2025-06-08 22:35:43,687 - INFO - Proceso completado
