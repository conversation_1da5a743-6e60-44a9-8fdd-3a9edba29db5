2025-06-08 22:35:19,263 - INFO - <PERSON><PERSON><PERSON><PERSON> datos para REPORTE_LOG_USUARIO desde 2025-05-05 hasta 2025-05-05
2025-06-08 22:35:19,263 - INFO - Procesando fecha: 2025-05-05
2025-06-08 22:35:19,288 - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-06-08 22:35:19,340 - INFO - Variables disponibles para reemplazo: ['year', 'month', 'day', 'next_year', 'next_month', 'next_day', 'fecha_inicio', 'fecha_fin', 'user_profile_bucket', 'user_profile_prefix', 'user_profile_region', 'user_profile_path', 'user_identifier_bucket', 'user_identifier_prefix', 'user_identifier_region', 'user_identifier_path', 'kyc_details_bucket', 'kyc_details_prefix', 'kyc_details_region', 'kyc_details_path', 'issuer_details_bucket', 'issuer_details_prefix', 'issuer_details_region', 'issuer_details_path', 'mtx_categories_bucket', 'mtx_categories_prefix', 'mtx_categories_region', 'mtx_categories_path', 'channel_grades_bucket', 'channel_grades_prefix', 'channel_grades_region', 'channel_grades_path', 'mtx_wallet_bucket', 'mtx_wallet_prefix', 'mtx_wallet_region', 'mtx_wallet_path', 'user_modification_history_bucket', 'user_modification_history_prefix', 'user_modification_history_region', 'user_modification_history_path', 'user_auth_change_history_bucket', 'user_auth_change_history_prefix', 'user_auth_change_history_region', 'user_auth_change_history_path', 'user_account_history_bucket', 'user_account_history_prefix', 'user_account_history_region', 'user_account_history_path']
2025-06-08 22:35:19,340 - INFO - Ejecutando query: log_usuarios_all
2025-06-08 22:35:19,439 - WARNING - Error al ejecutar la query original: Binder Error: Referenced column "MODIFIED_ON" not found in FROM clause!
Candidate bindings: "CREATED_ON", "APPROVED_BY", "OLD_DATA", "DATA_LAKE_PARTITION_DATE"
2025-06-08 22:35:19,439 - INFO - Aplicando solución para manejar JSON como texto (VARCHAR)...
2025-06-08 22:35:19,514 - ERROR - Error al aplicar la solución para tratar JSON como texto: Binder Error: Referenced column "MODIFIED_ON" not found in FROM clause!
Candidate bindings: "CREATED_ON", "APPROVED_BY", "OLD_DATA", "DATA_LAKE_PARTITION_DATE"
2025-06-08 22:35:19,514 - WARNING - Intentando solución alternativa...
2025-06-08 22:35:19,586 - ERROR - Error al aplicar la solución alternativa: Binder Error: Referenced column "MODIFIED_ON" not found in FROM clause!
Candidate bindings: "CREATED_ON", "APPROVED_BY", "OLD_DATA", "DATA_LAKE_PARTITION_DATE"
2025-06-08 22:35:19,586 - WARNING - Aplicando solución de emergencia (reemplazar operaciones JSON con NULL)...
2025-06-08 22:35:19,658 - ERROR - Error al aplicar solución de emergencia: Binder Error: Referenced column "MODIFIED_ON" not found in FROM clause!
Candidate bindings: "CREATED_ON", "APPROVED_BY", "OLD_DATA", "DATA_LAKE_PARTITION_DATE"
2025-06-08 22:35:19,658 - ERROR - Error al procesar la fecha 2025-05-05: Binder Error: Referenced column "MODIFIED_ON" not found in FROM clause!
Candidate bindings: "CREATED_ON", "APPROVED_BY", "OLD_DATA", "DATA_LAKE_PARTITION_DATE"
2025-06-08 22:35:19,658 - INFO - Proceso completado
