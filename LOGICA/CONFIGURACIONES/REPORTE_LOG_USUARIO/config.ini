[general]
output_dir = REPORTE_LOG_USUARIOS
log_dir = logs

[fecha]
# Si rango=true, usar rango_inicio y rango_fin
# Si rango=false, usar dias_atras
rango = true
dias_atras = 1
rango_inicio = 2025-06-06
rango_fin = 2025-06-06

[s3_sources]
# Para archivos consolidado_puro.parquet que no están particionados por fecha
# Usar prefijo vacío para que el framework no agregue /year/month/day
user_profile = prd-datalake-silver-zone-************, PDP_PROD10_MAINDB/USER_PROFILE_ORA/consolidado_puro.parquet, us-east-1
user_identifier = prd-datalake-silver-zone-************, PDP_PROD10_MAINDB/USER_IDENTIFIER_ORA/consolidado_puro.parquet, us-east-1
kyc_details = prd-datalake-silver-zone-************, PDP_PROD10_MAINDB/KYC_DETAILS_ORA/consolidado_puro.parquet, us-east-1
issuer_details = prd-datalake-silver-zone-************, PDP_PROD10_MAINDB/ISSUER_DETAILS_ORA/consolidado_puro.parquet, us-east-1
mtx_categories = prd-datalake-silver-zone-************, PDP_PROD10_MAINDB/MTX_CATEGORIES_ORA/consolidado_puro.parquet, us-east-1
channel_grades = prd-datalake-silver-zone-************, PDP_PROD10_MAINDB/CHANNEL_GRADES_ORA/consolidado_puro.parquet, us-east-1
mtx_wallet = prd-datalake-silver-zone-************, PDP_PROD10_MAINDBBUS/MTX_WALLET_ORA/consolidado_puro.parquet, us-east-1
user_modification_history = prd-datalake-silver-zone-************, PDP_PROD10_MAINDB/USER_MODIFICATION_HISTORY_ORA/consolidado_puro.parquet, us-east-1
user_auth_change_history = prd-datalake-silver-zone-************, PDP_PROD10_MAINDB/USER_AUTH_CHANGE_HISTORY_ORA/consolidado_puro.parquet, us-east-1
user_account_history = prd-datalake-silver-zone-************, APP_BIM_PROD_1/USER_ACCOUNT_HISTORY/consolidado_puro.parquet, us-east-1

[queries]
query_list = log_usuarios_completo

[post_processing]
scripts = REPORTES/REPORTE_LOG_USUARIO/postprocesar_log_usuario.py

[scripts]
scripts_list = post_process.py
output