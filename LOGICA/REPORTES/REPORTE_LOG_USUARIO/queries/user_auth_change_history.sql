-- Query para USER_AUTH_CHANGE_HISTORY - Equivalente a SP_USER_AUTH_DAY del pipeline original
-- Extrae cambios de autenticación para una fecha específica

SELECT
    uach.MODIFIED_ON,
    uach.MODIFICATION_TYPE,
    uach.MODIFIED_BY,
    uach.AUTHENTICATION_ID
FROM '{user_auth_change_history_path}' uach
WHERE CAST(uach.MODIFIED_ON AS DATE) = CAST('{fecha_inicio}' AS DATE)
    AND uach.AUTHENTICATION_TYPE = 'PIN'
