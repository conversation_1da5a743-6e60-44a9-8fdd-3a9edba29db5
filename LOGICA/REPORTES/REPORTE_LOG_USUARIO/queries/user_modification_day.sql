-- Query para USER_MODIFICATION_DAY - Equivalente a SP_USER_MODIFICATION del pipeline original
-- Extrae modificaciones de usuarios para una fecha específica

SELECT
    umh.REQUEST_TYPE,
    -- Tratar OLD_DATA y NEW_DATA como texto para evitar errores de JSON
    CASE
        WHEN TRY_CAST(UMH.OLD_DATA AS JSON) IS NOT NULL THEN UMH.OLD_DATA
        ELSE NULL
    END AS OLD_DATA,
    CASE
        WHEN TRY_CAST(UMH.NEW_DATA AS JSON) IS NOT NULL THEN UMH.NEW_DATA
        ELSE NULL
    END AS NEW_DATA,
    -- Extraer RAZON del JSON de manera segura
    CASE
        -- Solo intentar extraer JSON si el campo es un JSON válido
        WHEN UMH.NEW_DATA = 'CIERRE POR APP BIM' THEN 'CIERRE POR APP BIM'
        WHEN CAST(UMH.NEW_DATA AS VARCHAR) LIKE '%CIERRE POR APP BIM%' THEN 'CIERRE POR APP BIM'
        WHEN TRY_CAST(UMH.NEW_DATA AS JSON) IS NOT NULL THEN
            COALESCE(
                TRY_CAST(JSON_EXTRACT(UMH.NEW_DATA, '$.profileDetails.remarks') AS VARCHAR),
                TRY_CAST(JSON_EXTRACT(UMH.NEW_DATA, '$.profileDetails[0].remarks') AS VARCHAR)
            )
        ELSE NULL
    END AS RAZON,
    umh.USER_ID,
    umh.CREATED_BY,
    umh.CREATED_ON
FROM '{user_modification_history_path}' umh
WHERE CAST(umh.CREATED_ON AS DATE) = CAST('{fecha_inicio}' AS DATE)
