-- Query principal para LOG_USR (adaptado del pipeline)
-- Este query debe ser ejecutado después de generar los archivos temporales con los queries previos
WITH
WALLET_OLD AS (
    SELECT * FROM '{wallet_old_path}'
),
USER_MODIFICATION_DAY AS (
    SELECT * FROM '{user_modification_day_path}'
),
USER_DATA_TRX AS (
    SELECT * FROM '{user_data_trx_path}'
),
USER_AUTH_CHANGE_HISTORY AS (
    SELECT * FROM '{user_auth_change_history_path}'
)
SELECT
    -- Aquí debes replicar la lógica de joins y campos finales del pipeline
    *
FROM USER_MODIFICATION_DAY
-- Completar con los joins y lógica de negocio según el pipeline
;
