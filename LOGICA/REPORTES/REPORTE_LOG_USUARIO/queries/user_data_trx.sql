-- Query para extracción de USER_DATA_TRX (basado en pipeline_log_usuarios_duckdb.py)
WITH WALLETS AS (
    SELECT
        MW.USER_ID,
        COALESCE(MW.WALLET_NUMBER, 'N/A') AS WALLET_NUMBER,
        COALESCE(MW.ISSUER_ID, 0) AS ISSUER_ID,
        COALESCE(MW.USER_GRADE, 'DEFAULT') AS USER_GRADE,
        ROW_NUMBER() OVER (PARTITION BY MW.USER_ID ORDER BY COALESCE(MW.MODIFIED_ON, MW.CREATED_ON, '1900-01-01') DESC) AS ORDEN
    FROM '{mtx_wallet_path}' MW
    WHERE MW.USER_ID IS NOT NULL
)
SELECT
    CASE 
        WHEN UP.ATTR7 IS NOT NULL THEN UP.ATTR7
        ELSE UP.USER_ID
    END AS USER_ID,
    UP.USER_ID AS O_USER_ID,
    CASE 
        WHEN UP.ATTR7 IS NOT NULL THEN UP.ATTR7
        WHEN LENGTH(REPLACE(UP.USER_ID,'US.','')) > 15 THEN SUBSTR(REPLACE(UP.USER_ID,'US.',''), -15)
        ELSE REPLACE(UP.USER_ID,'US.','') 
    END AS USER_ID_M,
    COALESCE(UK.ID_TYPE, 'N/A') AS ID_TYPE,
    COALESCE(UK.ID_VALUE, UP.USER_CODE, UP.USER_ID) AS ID_VALUE,
    CASE 
        WHEN UP.ATTR8 IS NOT NULL THEN UP.ATTR8
        WHEN LENGTH(REPLACE(COALESCE(MW.WALLET_NUMBER, ''), 'UA.', '')) > 15 
            THEN SUBSTR(REPLACE(COALESCE(MW.WALLET_NUMBER, ''), 'UA.', ''), -15)
        ELSE REPLACE(COALESCE(MW.WALLET_NUMBER, ''), 'UA.', '')
    END AS WALLET_NUMBER,
    UP.STATUS,
    CASE 
        WHEN UP.MSISDN IN ('***********','***********','***********','***********') 
            THEN REPLACE(REPLACE(COALESCE(ID.ISSUER_CODE, 'DEFAULT'),'0144',''),'0231','') || ' MERCHANT GENERAL ACCOUNT PROFILE'
        WHEN UP.MSISDN IN ('***********','***********','***********','***********','***********','***********','***********','***********','***********','***********','***********','***********') 
            THEN UPPER(COALESCE(CG.GRADE_NAME, MW.USER_GRADE, '')) || ' PROFILE'
        ELSE REPLACE(REPLACE(COALESCE(ID.ISSUER_CODE, 'DEFAULT'),'0144',''),'0231','') || ' ' || UPPER(COALESCE(CG.GRADE_NAME, MW.USER_GRADE, ''))
    END AS GRADE_NAME,
    COALESCE(UP.MSISDN, '') AS MSISDN,
    COALESCE(UP.CREATED_ON, '1900-01-01') AS CREATED_ON,
    COALESCE(UP.CREATED_BY, 'SYSTEM') AS CREATED_BY,
    COALESCE(UP.REMARKS, '') AS REMARKS,
    UP.MODIFIED_ON AS STATUS_CHANGE_ON,
    REPLACE(COALESCE(ID.ISSUER_CODE, 'DEFAULT'),'0144','') AS ISSUER_CODE,
    COALESCE(UP.FIRST_NAME, UP.USER_CODE, 'N/A') AS FIRST_NAME,
    COALESCE(UP.LAST_NAME, UP.USER_CODE, 'N/A') AS LAST_NAME,
    COALESCE(MC.CATEGORY_NAME, UP.CATEGORY_ID, 'Final User') AS CATEGORY_NAME,
    CASE 
        WHEN COALESCE(MC.CATEGORY_NAME, UP.CATEGORY_ID) = 'Final User' THEN COALESCE(ID.ISSUER_CODE, 'DEFAULT') || ' ' || 'USUARIO FINAL'
        WHEN COALESCE(MC.CATEGORY_NAME, UP.CATEGORY_ID) = 'BIMER User' THEN COALESCE(ID.ISSUER_CODE, 'DEFAULT') || ' ' || 'BIMER'
        WHEN COALESCE(MC.CATEGORY_NAME, UP.CATEGORY_ID) = 'Virtual Agent' THEN COALESCE(ID.ISSUER_CODE, 'DEFAULT') || ' ' || 'AGENTE VIRTUAL'
        WHEN COALESCE(MC.CATEGORY_NAME, UP.CATEGORY_ID) = 'Agent' THEN COALESCE(ID.ISSUER_CODE, 'DEFAULT') || ' ' || 'AGENTE'
        WHEN COALESCE(MC.CATEGORY_NAME, UP.CATEGORY_ID) = 'Agencia' THEN COALESCE(ID.ISSUER_CODE, 'DEFAULT') || ' ' || 'AGENCIA'
        WHEN COALESCE(MC.CATEGORY_NAME, UP.CATEGORY_ID) = 'Super Agent' THEN COALESCE(ID.ISSUER_CODE, 'DEFAULT') || ' ' || 'SUPER AGENTE'
        WHEN UPPER(COALESCE(MC.CATEGORY_NAME, UP.CATEGORY_ID)) = 'REMESAS WU' THEN COALESCE(ID.ISSUER_CODE, 'DEFAULT') || ' ' || 'COMERCIO'
        WHEN COALESCE(MC.CATEGORY_NAME, UP.CATEGORY_ID) = 'Dispersor' THEN COALESCE(ID.ISSUER_CODE, 'DEFAULT') || ' ' || 'SUPER AGENTE'
        WHEN COALESCE(MC.CATEGORY_NAME, UP.CATEGORY_ID) = 'Biller' THEN 
            CASE 
                WHEN UP.MSISDN IN ('***********','***********','***********','***********') THEN COALESCE(ID.ISSUER_CODE, 'DEFAULT') || ' ' || 'COMERCIO'
                ELSE CASE
                    WHEN UP.MSISDN = '***********' THEN 'LINDLEY PROVEEDOR DE SERVICIOS'
                    WHEN UP.MSISDN = '***********' THEN 'BACKUS PROVEEDOR DE SERVICIOS'
                    WHEN UP.MSISDN IN ('***********','***********') THEN 'BITEL PROVEEDOR DE SERVICIOS'
                    WHEN UP.MSISDN IN ('***********','***********') THEN 'CLARO PROVEEDOR DE SERVICIOS'
                    WHEN UP.MSISDN = '***********' THEN 'ENTEL PROVEEDOR DE SERVICIOS'
                    WHEN UP.MSISDN = '***********' THEN 'GLORIA PROVEEDOR DE SERVICIOS'
                    WHEN UP.MSISDN = '***********' THEN 'MOVISTAR PROVEEDOR DE SERVICIOS'
                    WHEN UP.MSISDN = '***********' THEN 'UNIQUE PROVEEDOR DE SERVICIOS'
                    WHEN UP.MSISDN = '***********' THEN 'DIGIFLOW PROVEEDOR DE SERVICIOS'
                    WHEN UP.MSISDN = '***********' THEN 'SUNAT PROVEEDOR DE SERVICIOS'
                    ELSE COALESCE(ID.ISSUER_CODE, 'DEFAULT') || ' ' || COALESCE(MC.CATEGORY_NAME, UP.CATEGORY_ID, 'Final User')
                END 
            END
        ELSE COALESCE(ID.ISSUER_CODE, 'DEFAULT') || ' ' || COALESCE(MC.CATEGORY_NAME, UP.CATEGORY_ID, 'Final User')
    END AS PROFILE,
    CASE 
        WHEN COALESCE(MC.CATEGORY_NAME, UP.CATEGORY_ID) = 'Final User' THEN 'USUARIO FINAL'
        WHEN COALESCE(MC.CATEGORY_NAME, UP.CATEGORY_ID) = 'BIMER User' THEN 'BIMER'
        WHEN COALESCE(MC.CATEGORY_NAME, UP.CATEGORY_ID) = 'Virtual Agent' THEN 'AGENTE VIRTUAL'
        WHEN COALESCE(MC.CATEGORY_NAME, UP.CATEGORY_ID) = 'Agent' THEN 'AGENTE'
        WHEN COALESCE(MC.CATEGORY_NAME, UP.CATEGORY_ID) = 'Agencia' THEN 'AGENCIA'
        WHEN COALESCE(MC.CATEGORY_NAME, UP.CATEGORY_ID) = 'Super Agent' THEN 'SUPER AGENTE'
        WHEN UPPER(COALESCE(MC.CATEGORY_NAME, UP.CATEGORY_ID)) = 'REMESAS WU' THEN 'COMERCIO'
        WHEN COALESCE(MC.CATEGORY_NAME, UP.CATEGORY_ID) = 'Dispersor' THEN 'SUPER AGENTE'
        WHEN COALESCE(MC.CATEGORY_NAME, UP.CATEGORY_ID) = 'Biller' THEN 
            CASE 
                WHEN UP.MSISDN IN ('***********','***********','***********','***********') THEN 'COMERCIO'
                ELSE CASE
                    WHEN UP.MSISDN = '***********' THEN 'LINDLEY PROVEEDOR DE SERVICIOS'
                    WHEN UP.MSISDN = '***********' THEN 'BACKUS PROVEEDOR DE SERVICIOS'
                    WHEN UP.MSISDN IN ('***********','***********') THEN 'BITEL PROVEEDOR DE SERVICIOS'
                    WHEN UP.MSISDN IN ('***********','***********') THEN 'CLARO PROVEEDOR DE SERVICIOS'
                    WHEN UP.MSISDN = '***********' THEN 'ENTEL PROVEEDOR DE SERVICIOS'
                    WHEN UP.MSISDN = '***********' THEN 'GLORIA PROVEEDOR DE SERVICIOS'
                    WHEN UP.MSISDN = '***********' THEN 'MOVISTAR PROVEEDOR DE SERVICIOS'
                    WHEN UP.MSISDN = '***********' THEN 'UNIQUE PROVEEDOR DE SERVICIOS'
                    WHEN UP.MSISDN = '***********' THEN 'DIGIFLOW PROVEEDOR DE SERVICIOS'
                    WHEN UP.MSISDN = '***********' THEN 'SUNAT PROVEEDOR DE SERVICIOS'
                    ELSE COALESCE(MC.CATEGORY_NAME, UP.CATEGORY_ID, 'Final User')
                END 
            END
        ELSE COALESCE(MC.CATEGORY_NAME, UP.CATEGORY_ID, 'Final User')
    END AS PROFILE_TRX,
    COALESCE(UP.ATTR1, '') AS ATTR1,
    COALESCE(UP.PREFERRED_LANG, 'ES') AS PREFERRED_LANG,
    COALESCE(UP.USER_CODE, UP.USER_ID) AS USER_CODE,
    COALESCE(UP.LOGIN_ID, UP.USER_ID) AS LOGIN_ID,
    COALESCE(CAST(UP.WORKSPACE_ID AS VARCHAR), '1') AS WORKSPACE_ID
FROM '{user_profile_path}' UP
INNER JOIN '{kyc_details_path}' UK ON UP.KYC_ID = UK.KYC_ID
LEFT JOIN WALLETS MW ON UP.USER_ID = MW.USER_ID AND MW.ORDEN = 1
LEFT JOIN '{issuer_details_path}' ID ON MW.ISSUER_ID = ID.ISSUER_ID
LEFT JOIN '{mtx_categories_path}' MC ON CAST(UP.CATEGORY_ID AS VARCHAR) = CAST(MC.CATEGORY_CODE AS VARCHAR)
LEFT JOIN '{channel_grades_path}' CG ON CAST(MW.USER_GRADE AS VARCHAR) = CAST(CG.GRADE_CODE AS VARCHAR)
WHERE UP.USER_ID IS NOT NULL
    AND CAST(UP.CREATED_ON AS DATE) <= CAST('{fecha_inicio}' AS DATE)
LEFT JOIN '{mtx_categories_path}' MC ON CAST(UP.CATEGORY_ID AS VARCHAR) = CAST(MC.CATEGORY_CODE AS VARCHAR)
LEFT JOIN '{channel_grades_path}' CG ON CAST(MW.USER_GRADE AS VARCHAR) = CAST(CG.GRADE_CODE AS VARCHAR)
WHERE UP.USER_ID IS NOT NULL
  AND CAST(UP.CREATED_ON AS DATE) <= CAST('{fecha}' AS DATE);
