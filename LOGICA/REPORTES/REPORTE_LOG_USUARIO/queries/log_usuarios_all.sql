-- Query principal para el reporte de LOG_USUARIO - Versión simplificada para probar
-- Comenzando solo con USER_MODIFICATION_DAY como en el pipeline original

SELECT 
    USER_ID,
    CREATED_ON,
    'USER_MODIFICATION' as TIPO_EVENTO,
    OLD_DATA,
    NEW_DATA,
    APPROVED_BY as CREATED_BY
FROM 's3://{user_modification_history_bucket}/{user_modification_history_prefix}/consolidado_puro.parquet'
WHERE CAST(CREATED_ON AS DATE) = CAST('{fecha_inicio}' AS DATE)
LIMIT 1000;
