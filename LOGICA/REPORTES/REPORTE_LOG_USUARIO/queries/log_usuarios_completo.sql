-- Query principal LOG_USUARIOS_ALL - VERSIÓN COMPLETA EXACTA AL ORIGINAL
-- Replica línea por línea la lógica del SP_LOG_USR del pipeline original
-- Co<PERSON>ina múltiples fuentes: USER_MODIFICATION, USER_AUTH_CHANGE, Activate<PERSON>ser, A<PERSON>liaUser, ClosedAccount

-- PASO 1: <PERSON><PERSON><PERSON> WALLET_OLD temporal para datos históricos
WITH WALLET_OLD AS (
    SELECT 
        UD.USER_ID,
        UD.ATTR7_OLD,
        UD.ATTR8_OLD,
        UD.CREATED_AT,
        ID.ISSUER_CODE,
        UD.GRADE_OLD AS GRADE_NAME_OLD,
        ROW_NUMBER() OVER(PARTITION BY UD.USER_ID ORDER BY UD.CREATED_AT DESC) AS ORDEN
    FROM '{user_account_history_path}' UD 
    INNER JOIN '{issuer_details_path}' ID ON UD.ISSUER_OLD = ID.ISSUER_ID
),

-- PASO 2: Crear USER_DATA_TRX temporal (datos consolidados de usuarios)
USER_DATA_TRX AS (
    SELECT
        CASE 
            WHEN UP.ATTR7 IS NOT NULL THEN UP.ATTR7
            ELSE UP.USER_ID
        END AS USER_ID,
        UP.USER_ID AS O_USER_ID,
        CASE 
            WHEN UP.ATTR7 IS NOT NULL THEN UP.ATTR7
            WHEN LENGTH(REPLACE(UP.USER_ID,'US.','')) > 15 THEN SUBSTR(REPLACE(UP.USER_ID,'US.',''), -15)
            ELSE REPLACE(UP.USER_ID,'US.','') 
        END AS USER_ID_M,
        COALESCE(UK.ID_TYPE, 'N/A') AS ID_TYPE,
        COALESCE(UK.ID_VALUE, UP.USER_CODE, UP.USER_ID) AS ID_VALUE,
        CASE 
            WHEN UP.ATTR8 IS NOT NULL THEN UP.ATTR8
            WHEN LENGTH(REPLACE(COALESCE(MW.WALLET_NUMBER, ''), 'UA.', '')) > 15 
                THEN SUBSTR(REPLACE(COALESCE(MW.WALLET_NUMBER, ''), 'UA.', ''), -15)
            ELSE REPLACE(COALESCE(MW.WALLET_NUMBER, ''), 'UA.', '')
        END AS WALLET_NUMBER,
        UP.STATUS,
        CASE 
            WHEN UP.MSISDN IN ('***********','***********','***********','***********') 
                THEN REPLACE(REPLACE(COALESCE(ID.ISSUER_CODE, 'DEFAULT'),'0144',''),'0231','') || ' MERCHANT GENERAL ACCOUNT PROFILE'
            WHEN UP.MSISDN IN ('***********','***********','***********','***********','***********','***********','***********','***********','***********','***********','***********','***********') 
                THEN UPPER(COALESCE(CG.GRADE_NAME, MW.USER_GRADE, '')) || ' PROFILE'
            ELSE REPLACE(REPLACE(COALESCE(ID.ISSUER_CODE, 'DEFAULT'),'0144',''),'0231','') || ' ' || UPPER(COALESCE(CG.GRADE_NAME, MW.USER_GRADE, ''))
        END AS GRADE_NAME,
        COALESCE(UP.MSISDN, '') AS MSISDN,
        COALESCE(UP.CREATED_ON, '1900-01-01') AS CREATED_ON,
        COALESCE(UP.CREATED_BY, 'SYSTEM') AS CREATED_BY,
        COALESCE(UP.REMARKS, '') AS REMARKS,
        UP.MODIFIED_ON AS STATUS_CHANGE_ON,
        REPLACE(COALESCE(ID.ISSUER_CODE, 'DEFAULT'),'0144','') AS ISSUER_CODE,
        COALESCE(UP.FIRST_NAME, UP.USER_CODE, 'N/A') AS FIRST_NAME,
        COALESCE(UP.LAST_NAME, UP.USER_CODE, 'N/A') AS LAST_NAME,
        COALESCE(MC.CATEGORY_NAME, UP.CATEGORY_ID, 'Final User') AS CATEGORY_NAME,
        CASE 
            WHEN COALESCE(MC.CATEGORY_NAME, UP.CATEGORY_ID) = 'Final User' THEN COALESCE(ID.ISSUER_CODE, 'DEFAULT') || ' ' || 'USUARIO FINAL'
            WHEN COALESCE(MC.CATEGORY_NAME, UP.CATEGORY_ID) = 'BIMER User' THEN COALESCE(ID.ISSUER_CODE, 'DEFAULT') || ' ' || 'BIMER'
            WHEN COALESCE(MC.CATEGORY_NAME, UP.CATEGORY_ID) = 'Virtual Agent' THEN COALESCE(ID.ISSUER_CODE, 'DEFAULT') || ' ' || 'AGENTE VIRTUAL'
            WHEN COALESCE(MC.CATEGORY_NAME, UP.CATEGORY_ID) = 'Agent' THEN COALESCE(ID.ISSUER_CODE, 'DEFAULT') || ' ' || 'AGENTE'
            WHEN COALESCE(MC.CATEGORY_NAME, UP.CATEGORY_ID) = 'Agencia' THEN COALESCE(ID.ISSUER_CODE, 'DEFAULT') || ' ' || 'AGENCIA'
            WHEN COALESCE(MC.CATEGORY_NAME, UP.CATEGORY_ID) = 'Super Agent' THEN COALESCE(ID.ISSUER_CODE, 'DEFAULT') || ' ' || 'SUPER AGENTE'
            WHEN UPPER(COALESCE(MC.CATEGORY_NAME, UP.CATEGORY_ID)) = 'REMESAS WU' THEN COALESCE(ID.ISSUER_CODE, 'DEFAULT') || ' ' || 'COMERCIO'
            WHEN COALESCE(MC.CATEGORY_NAME, UP.CATEGORY_ID) = 'Dispersor' THEN COALESCE(ID.ISSUER_CODE, 'DEFAULT') || ' ' || 'SUPER AGENTE'
            WHEN COALESCE(MC.CATEGORY_NAME, UP.CATEGORY_ID) = 'Biller' THEN 
                CASE 
                    WHEN UP.MSISDN IN ('***********','***********','***********','***********') THEN COALESCE(ID.ISSUER_CODE, 'DEFAULT') || ' ' || 'COMERCIO'
                    ELSE CASE
                        WHEN UP.MSISDN = '***********' THEN 'LINDLEY PROVEEDOR DE SERVICIOS'
                        WHEN UP.MSISDN = '***********' THEN 'BACKUS PROVEEDOR DE SERVICIOS'
                        WHEN UP.MSISDN IN ('***********','***********') THEN 'BITEL PROVEEDOR DE SERVICIOS'
                        WHEN UP.MSISDN IN ('***********','***********') THEN 'CLARO PROVEEDOR DE SERVICIOS'
                        WHEN UP.MSISDN = '***********' THEN 'ENTEL PROVEEDOR DE SERVICIOS'
                        WHEN UP.MSISDN = '***********' THEN 'GLORIA PROVEEDOR DE SERVICIOS'
                        WHEN UP.MSISDN = '***********' THEN 'MOVISTAR PROVEEDOR DE SERVICIOS'
                        WHEN UP.MSISDN = '***********' THEN 'UNIQUE PROVEEDOR DE SERVICIOS'
                        WHEN UP.MSISDN = '***********' THEN 'DIGIFLOW PROVEEDOR DE SERVICIOS'
                        WHEN UP.MSISDN = '***********' THEN 'SUNAT PROVEEDOR DE SERVICIOS'
                        ELSE COALESCE(ID.ISSUER_CODE, 'DEFAULT') || ' ' || COALESCE(MC.CATEGORY_NAME, UP.CATEGORY_ID, 'Final User')
                    END 
                END
            ELSE COALESCE(ID.ISSUER_CODE, 'DEFAULT') || ' ' || COALESCE(MC.CATEGORY_NAME, UP.CATEGORY_ID, 'Final User')
        END AS PROFILE,
        CASE 
            WHEN COALESCE(MC.CATEGORY_NAME, UP.CATEGORY_ID) = 'Final User' THEN 'USUARIO FINAL'
            WHEN COALESCE(MC.CATEGORY_NAME, UP.CATEGORY_ID) = 'BIMER User' THEN 'BIMER'
            WHEN COALESCE(MC.CATEGORY_NAME, UP.CATEGORY_ID) = 'Virtual Agent' THEN 'AGENTE VIRTUAL'
            WHEN COALESCE(MC.CATEGORY_NAME, UP.CATEGORY_ID) = 'Agent' THEN 'AGENTE'
            WHEN COALESCE(MC.CATEGORY_NAME, UP.CATEGORY_ID) = 'Agencia' THEN 'AGENCIA'
            WHEN COALESCE(MC.CATEGORY_NAME, UP.CATEGORY_ID) = 'Super Agent' THEN 'SUPER AGENTE'
            WHEN UPPER(COALESCE(MC.CATEGORY_NAME, UP.CATEGORY_ID)) = 'REMESAS WU' THEN 'COMERCIO'
            WHEN COALESCE(MC.CATEGORY_NAME, UP.CATEGORY_ID) = 'Dispersor' THEN 'SUPER AGENTE'
            WHEN COALESCE(MC.CATEGORY_NAME, UP.CATEGORY_ID) = 'Biller' THEN 
                CASE 
                    WHEN UP.MSISDN IN ('***********','***********','***********','***********') THEN 'COMERCIO'
                    ELSE CASE
                        WHEN UP.MSISDN = '***********' THEN 'LINDLEY PROVEEDOR DE SERVICIOS'
                        WHEN UP.MSISDN = '***********' THEN 'BACKUS PROVEEDOR DE SERVICIOS'
                        WHEN UP.MSISDN IN ('***********','***********') THEN 'BITEL PROVEEDOR DE SERVICIOS'
                        WHEN UP.MSISDN IN ('***********','***********') THEN 'CLARO PROVEEDOR DE SERVICIOS'
                        WHEN UP.MSISDN = '***********' THEN 'ENTEL PROVEEDOR DE SERVICIOS'
                        WHEN UP.MSISDN = '***********' THEN 'GLORIA PROVEEDOR DE SERVICIOS'
                        WHEN UP.MSISDN = '***********' THEN 'MOVISTAR PROVEEDOR DE SERVICIOS'
                        WHEN UP.MSISDN = '***********' THEN 'UNIQUE PROVEEDOR DE SERVICIOS'
                        WHEN UP.MSISDN = '***********' THEN 'DIGIFLOW PROVEEDOR DE SERVICIOS'
                        WHEN UP.MSISDN = '***********' THEN 'SUNAT PROVEEDOR DE SERVICIOS'
                        ELSE COALESCE(MC.CATEGORY_NAME, UP.CATEGORY_ID, 'Final User')
                    END 
                END
            ELSE COALESCE(MC.CATEGORY_NAME, UP.CATEGORY_ID, 'Final User')
        END AS PROFILE_TRX,
        COALESCE(UP.ATTR1, '') AS ATTR1,
        COALESCE(UP.PREFERRED_LANG, 'ES') AS PREFERRED_LANG,
        COALESCE(UP.USER_CODE, UP.USER_ID) AS USER_CODE,
        COALESCE(UP.LOGIN_ID, UP.USER_ID) AS LOGIN_ID,
        COALESCE(CAST(UP.WORKSPACE_ID AS VARCHAR), '1') AS WORKSPACE_ID
    FROM '{user_profile_path}' UP
    INNER JOIN '{kyc_details_path}' UK ON UP.KYC_ID = UK.KYC_ID
    LEFT JOIN (
        SELECT
            MW.USER_ID,
            COALESCE(MW.WALLET_NUMBER, 'N/A') AS WALLET_NUMBER,
            COALESCE(MW.ISSUER_ID, 0) AS ISSUER_ID,
            COALESCE(MW.USER_GRADE, 'DEFAULT') AS USER_GRADE,
            ROW_NUMBER() OVER (PARTITION BY MW.USER_ID ORDER BY COALESCE(MW.MODIFIED_ON, MW.CREATED_ON, '1900-01-01') DESC) AS ORDEN
        FROM '{mtx_wallet_path}' MW
        WHERE MW.USER_ID IS NOT NULL
    ) MW ON UP.USER_ID = MW.USER_ID AND MW.ORDEN = 1
    LEFT JOIN '{issuer_details_path}' ID ON MW.ISSUER_ID = ID.ISSUER_ID
    LEFT JOIN '{mtx_categories_path}' MC ON CAST(UP.CATEGORY_ID AS VARCHAR) = CAST(MC.CATEGORY_CODE AS VARCHAR)
    LEFT JOIN '{channel_grades_path}' CG ON CAST(MW.USER_GRADE AS VARCHAR) = CAST(CG.GRADE_CODE AS VARCHAR)
    WHERE UP.USER_ID IS NOT NULL
        AND CAST(UP.CREATED_ON AS DATE) <= CAST('{fecha_inicio}' AS DATE)
),

-- PASO 3: Crear USER_MODIFICATION_DAY temporal
USER_MODIFICATION_DAY AS (
    SELECT 
        umh.REQUEST_TYPE,
        umh.OLD_DATA AS OLD_DATA,
        umh.NEW_DATA AS NEW_DATA,
        CASE
            WHEN umh.NEW_DATA = 'CIERRE POR APP BIM' THEN 'CIERRE POR APP BIM'
            WHEN CAST(umh.NEW_DATA AS VARCHAR) LIKE '%CIERRE POR APP BIM%' THEN 'CIERRE POR APP BIM'
            ELSE NULL
        END AS RAZON,
        umh.USER_ID,
        umh.CREATED_BY,
        umh.CREATED_ON
    FROM '{user_modification_history_path}' umh
    WHERE CAST(umh.CREATED_ON AS DATE) = CAST('{fecha_inicio}' AS DATE)
),

-- PASO 4: Crear USER_AUTH_CHANGE_HISTORY temporal
USER_AUTH_CHANGE_HISTORY AS (
    SELECT 
        uach.MODIFIED_ON,
        uach.MODIFICATION_TYPE,
        uach.MODIFIED_BY,
        uach.AUTHENTICATION_ID
    FROM '{user_auth_change_history_path}' uach
    WHERE CAST(uach.MODIFIED_ON AS DATE) = CAST('{fecha_inicio}' AS DATE)
        AND uach.AUTHENTICATION_TYPE = 'PIN'
),

-- PASO 5: PROCESS - Combinar todas las fuentes (EXACTO como Oracle SP_LOG_USR líneas 47-170)
PROCESS AS (
    -- PARTE 1: User Modifications (EXACTO como Oracle líneas 47-77)
    SELECT
        'UM.' || CAST(ROW_NUMBER() OVER() AS VARCHAR) AS userHistId,
        umh.created_on AS createdOn,
        ud.ID_TYPE AS TipoDocumento,
        ud.ID_VALUE AS Documento,
        ud.MSISDN AS Msisdn,
        NULL AS MsisdnB,
        ud.ISSUER_CODE AS BankDomain,
        CASE
            WHEN UMH.REQUEST_TYPE IN ('Resume User','Unlock Wallet') THEN 'ID:awspdp/ADMIN'
            ELSE REPLACE(REPLACE(umh.created_by,'US.',''),'SELF','ID:unknown/SERVICE')
        END AS created_by,
        ud.USER_ID_M AS userId,
        'MOBILE_MONEY' AS accountType,
        ud.WALLET_NUMBER AS accountId,
        ud.FIRST_NAME AS Nombre,
        ud.LAST_NAME AS Apellido,
        NULL AS NNombre,
        NULL AS NApellido,
        REPLACE(REPLACE(ud.profile,'0231',''),'0144','') as perfilA,
        NULL AS perfilB,
        ud.PREFERRED_LANG AS IdiomaA,
        NULL AS IdiomaB,
        ud.ATTR1 AS TelcoA,
        NULL AS TelcoB,
        umh.razon,
        ud.grade_name as PerfilCuenta,
        ud.grade_name as PerfilCuentaA,
        NULL AS perfilCuentaB,
        ud.ID_TYPE AS TipoDocumentoA,
        NULL AS TipoDocumentoB,
        ud.ID_VALUE AS DocumentoB,
        NULL AS NumDocumentoB,
        umh.request_type AS requestType,
        CAST(umh.old_data AS VARCHAR) AS oldData,
        CAST(umh.new_data AS VARCHAR) AS newData,
        UD.O_USER_ID
    FROM USER_MODIFICATION_DAY umh
    INNER JOIN USER_DATA_TRX ud ON umh.user_id = ud.O_USER_ID

    UNION ALL

    -- PARTE 2: User Auth Change History (EXACTO como Oracle líneas 79-110)
    SELECT
        uach.AUTHENTICATION_ID AS userHistId,
        uach.MODIFIED_ON AS createdOn,
        COALESCE(ud.ID_TYPE, '') AS TipoDocumento,
        COALESCE(ud.ID_VALUE, '') AS Documento,
        COALESCE(ud.MSISDN, '') AS Msisdn,
        NULL AS MsisdnB,
        CASE WHEN COALESCE(ud.ISSUER_CODE, 'DEFAULT') = 'DEFAULT' THEN NULL ELSE ud.ISSUER_CODE END AS BankDomain,
        CASE
            WHEN UACH.MODIFICATION_TYPE = 'RESET_AUTH_VALUE' THEN 'ID:unknown/SERVICE'
            ELSE REPLACE(REPLACE(uach.MODIFIED_BY,'US.',''),'SELF','ID:unknown/SERVICE')
        END AS created_by,
        COALESCE(ud.USER_ID_M, '') AS userId,
        'MOBILE_MONEY' AS accountType,
        COALESCE(ud.WALLET_NUMBER, '') AS accountId,
        COALESCE(ud.FIRST_NAME, '') AS Nombre,
        COALESCE(ud.LAST_NAME, '') AS Apellido,
        NULL AS NNombre,
        NULL AS NApellido,
        REPLACE(REPLACE(COALESCE(ud.profile, ''),'0231',''),'0144','') as perfilA,
        NULL AS perfilB,
        COALESCE(ud.PREFERRED_LANG, '') AS IdiomaA,
        NULL AS IdiomaB,
        COALESCE(ud.ATTR1, '') AS TelcoA,
        NULL AS TelcoB,
        NULL AS Razon,
        COALESCE(ud.grade_name, '') as PerfilCuenta,
        COALESCE(ud.grade_name, '') as PerfilCuentaA,
        NULL AS perfilCuentaB,
        COALESCE(ud.ID_TYPE, '') AS TipoDocumentoA,
        NULL AS TipoDocumentoB,
        COALESCE(ud.ID_VALUE, '') AS DocumentoB,
        NULL AS NumDocumentoB,
        uach.MODIFICATION_TYPE AS requestType,
        NULL AS oldData,
        NULL AS newData,
        COALESCE(UD.O_USER_ID, uach.AUTHENTICATION_ID) AS O_USER_ID
    FROM USER_AUTH_CHANGE_HISTORY uach
    LEFT JOIN '{user_identifier_path}' ui ON uach.AUTHENTICATION_ID = ui.AUTHENTICATION_ID
    LEFT JOIN USER_DATA_TRX ud ON ui.USER_ID = ud.O_USER_ID

    UNION ALL

    -- PARTE 3: ActivateUser + AfiliaUser (EXACTO como Oracle líneas 112-140)
    SELECT
        ud.O_USER_ID AS userHistId,
        ud.CREATED_ON AS createdOn,
        CASE WHEN UD.PROFILE LIKE '%PROVEEDOR%' THEN '' ELSE ud.ID_TYPE END AS TipoDocumento,
        CASE WHEN UD.PROFILE LIKE '%PROVEEDOR%' THEN '' ELSE ud.ID_VALUE END AS Documento,
        ud.MSISDN AS Msisdn,
        NULL AS MsisdnB,
        CASE WHEN ud.ISSUER_CODE = 'DEFAULT' THEN NULL ELSE ud.ISSUER_CODE END AS BankDomain,
        REPLACE(REPLACE(ud.CREATED_BY,'US.',''),'SELF','ID:unknown/SERVICE') AS created_by,
        ud.USER_ID_M AS userId,
        'MOBILE_MONEY' AS accountType,
        ud.WALLET_NUMBER AS accountId,
        ud.FIRST_NAME AS Nombre,
        ud.LAST_NAME AS Apellido,
        NULL AS NNombre,
        NULL AS NApellido,
        REPLACE(REPLACE(ud.profile,'0231',''),'0144','') as perfilA,
        NULL AS perfilB,
        ud.PREFERRED_LANG AS IdiomaA,
        NULL AS IdiomaB,
        ud.ATTR1 AS TelcoA,
        NULL AS TelcoB,
        NULL AS Razon,
        CASE WHEN ud.grade_name LIKE '%GENERAL%' THEN REPLACE(ud.grade_name,'NORMAL GENERAL','NORMAL') ELSE ud.grade_name END as PerfilCuenta,
        CASE WHEN ud.grade_name LIKE '%GENERAL%' THEN REPLACE(ud.grade_name,'NORMAL GENERAL','NORMAL') ELSE ud.grade_name END as PerfilCuentaA,
        NULL AS perfilCuentaB,
        ud.ID_TYPE AS TipoDocumentoA,
        NULL AS TipoDocumentoB,
        ud.ID_VALUE AS DocumentoB,
        NULL AS NumDocumentoB,
        req.requestType AS requestType,
        NULL AS oldData,
        NULL AS newData,
        UD.O_USER_ID
    FROM USER_DATA_TRX ud
    CROSS JOIN (SELECT 'ActivateUser' AS requestType FROM (VALUES (1)) AS t(x)
                UNION ALL
                SELECT 'AfiliaUser' FROM (VALUES (1)) AS t(x)) req
    WHERE CAST(ud.CREATED_ON AS DATE) = CAST('{fecha_inicio}' AS DATE)

    UNION ALL

    -- PARTE 4: ClosedAccount + ClosedUserAccount (EXACTO como Oracle líneas 142-170)
    SELECT
        ud.O_USER_ID AS userHistId,
        ud.STATUS_CHANGE_ON AS createdOn,
        ud.ID_TYPE AS TipoDocumento,
        ud.ID_VALUE || 'X' || ud.USER_ID_M AS Documento,
        ud.MSISDN AS Msisdn,
        NULL AS MsisdnB,
        CASE WHEN ud.ISSUER_CODE = 'DEFAULT' THEN NULL ELSE ud.ISSUER_CODE END AS BankDomain,
        REPLACE(REPLACE(ud.CREATED_BY,'US.',''),'SELF','ID:unknown/SERVICE') AS created_by,
        ud.USER_ID_M AS userId,
        'MOBILE_MONEY' AS accountType,
        ud.WALLET_NUMBER AS accountId,
        ud.FIRST_NAME AS Nombre,
        ud.LAST_NAME AS Apellido,
        NULL AS NNombre,
        NULL AS NApellido,
        REPLACE(REPLACE(ud.profile,'0231',''),'0144','') as perfilA,
        NULL AS perfilB,
        ud.PREFERRED_LANG AS IdiomaA,
        NULL AS IdiomaB,
        ud.ATTR1 AS TelcoA,
        NULL AS TelcoB,
        ud.remarks AS Razon,
        ud.grade_name as PerfilCuenta,
        ud.grade_name as PerfilCuentaA,
        NULL AS perfilCuentaB,
        ud.ID_TYPE AS TipoDocumentoA,
        NULL AS TipoDocumentoB,
        ud.ID_VALUE AS DocumentoB,
        NULL AS NumDocumentoB,
        req.requestType AS requestType,
        NULL AS oldData,
        NULL AS newData,
        UD.O_USER_ID
    FROM USER_DATA_TRX ud
    CROSS JOIN (SELECT 'ClosedAccount' AS requestType FROM (VALUES (1)) AS t(x)
                UNION ALL
                SELECT 'ClosedUserAccount' FROM (VALUES (1)) AS t(x)) req
    WHERE ud.STATUS = 'N'
    AND CAST(ud.STATUS_CHANGE_ON AS DATE) = CAST('{fecha_inicio}' AS DATE)
)

-- SELECT FINAL (EXACTO como Oracle líneas 172-206) - COLUMNAS EN MAYÚSCULAS
SELECT
    P.userHistId AS USERHISTID,
    P.createdOn AS CREATEDON,
    P.TipoDocumento AS TIPODOCUMENTO,
    P.Documento AS DOCUMENTO,
    P.Msisdn AS MSISDN,
    P.MsisdnB AS MSISDNB,
    CASE
        WHEN DA.USER_ID IS NOT NULL THEN
            CASE WHEN DA.ISSUER_CODE = 'DEFAULT' THEN NULL ELSE DA.ISSUER_CODE END
        ELSE P.BankDomain
    END AS BANKDOMAIN,
    P.created_by AS CREATED_BY,
    CASE
        WHEN DA.USER_ID IS NOT NULL THEN DA.ATTR7_OLD
        ELSE P.userId
    END AS USERID,
    P.accountType AS ACCOUNTTYPE,
    CASE
        WHEN DA.USER_ID IS NOT NULL THEN DA.ATTR8_OLD
        ELSE P.accountId
    END AS ACCOUNTID,
    P.Nombre AS NOMBRE,
    P.Apellido AS APELLIDO,
    P.NNombre AS NNOMBRE,
    P.NApellido AS NAPELLIDO,
    CASE
        WHEN DA.USER_ID IS NOT NULL AND STRPOS(P.perfilA, ' ') > 0
        THEN UPPER(DA.ISSUER_CODE || ' ' || SUBSTR(P.perfilA, STRPOS(P.perfilA, ' ') + 1))
        ELSE UPPER(P.perfilA)
    END AS PERFILA,
    P.perfilB AS PERFILB,
    P.IdiomaA AS IDIOMAA,
    P.IdiomaB AS IDIOMAB,
    P.TelcoA AS TELCOA,
    P.TelcoB AS TELCOB,
    P.Razon AS RAZON,
    CASE
        WHEN DA.USER_ID IS NOT NULL THEN UPPER(DA.ISSUER_CODE || ' ' || DA.GRADE_NAME_OLD)
        ELSE P.PerfilCuenta
    END AS PERFILCUENTA,
    CASE
        WHEN DA.USER_ID IS NOT NULL THEN UPPER(DA.ISSUER_CODE || ' ' || DA.GRADE_NAME_OLD)
        ELSE P.PerfilCuentaA
    END AS PERFILCUENTAA,
    P.perfilCuentaB AS PERFILCUENTAB,
    P.TipoDocumentoA AS TIPODOCUMENTOA,
    P.TipoDocumentoB AS TIPODOCUMENTOB,
    P.DocumentoB AS DOCUMENTOB,
    P.NumDocumentoB AS NUMDOCUMENTOB,
    P.requestType AS REQUESTTYPE,
    P.oldData AS OLDDATA,
    P.newData AS NEWDATA,
    DA2.ATTR7_OLD AS USERIDOLD,
    DA2.ATTR8_OLD AS ACCOUNTIDOLD
FROM PROCESS P
LEFT JOIN WALLET_OLD DA ON P.O_USER_ID = DA.USER_ID
    AND STRFTIME('%Y-%m-%d %H:%M', CAST(P.createdOn AS TIMESTAMP)) < STRFTIME('%Y-%m-%d %H:%M', CAST(DA.CREATED_AT AS TIMESTAMP))
    AND DA.ORDEN = 1
LEFT JOIN WALLET_OLD DA2 ON P.O_USER_ID = DA2.USER_ID
    AND DA2.ORDEN = 1
