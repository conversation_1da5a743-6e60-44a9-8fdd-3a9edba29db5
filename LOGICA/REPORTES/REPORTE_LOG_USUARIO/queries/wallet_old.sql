-- Query para extracción de WALLET_OLD (usado en el join final de LOG_USR)
SELECT 
    UD.USER_ID,
    UD.ATTR7_OLD,
    UD.ATTR8_OLD,
    UD.CREATED_AT,
    ID.ISSUER_CODE,
    UD.GRADE_OLD AS GRADE_NAME_OLD,
    ROW_NUMBER() OVER(PARTITION BY UD.USER_ID ORDER BY UD.CREATED_AT DESC) AS ORDEN
FROM '{user_account_history_path}' UD 
INNER JOIN '{issuer_details_path}' ID ON UD.ISSUER_OLD = ID.ISSUER_ID;
