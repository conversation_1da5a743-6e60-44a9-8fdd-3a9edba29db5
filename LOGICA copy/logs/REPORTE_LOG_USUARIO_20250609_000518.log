2025-06-09 00:05:18,737 - INFO - <PERSON><PERSON><PERSON><PERSON> datos para REPORTE_LOG_USUARIO desde 2025-06-05 hasta 2025-06-05
2025-06-09 00:05:18,737 - INFO - Procesando fecha: 2025-06-05
2025-06-09 00:05:18,767 - INFO - Found credentials from IAM Role: ec-analytics-reporting-EC2-Role
2025-06-09 00:05:18,819 - INFO - Variables disponibles para reemplazo: ['year', 'month', 'day', 'next_year', 'next_month', 'next_day', 'fecha_inicio', 'fecha_fin', 'user_profile_bucket', 'user_profile_prefix', 'user_profile_region', 'user_profile_path', 'user_identifier_bucket', 'user_identifier_prefix', 'user_identifier_region', 'user_identifier_path', 'kyc_details_bucket', 'kyc_details_prefix', 'kyc_details_region', 'kyc_details_path', 'issuer_details_bucket', 'issuer_details_prefix', 'issuer_details_region', 'issuer_details_path', 'mtx_categories_bucket', 'mtx_categories_prefix', 'mtx_categories_region', 'mtx_categories_path', 'channel_grades_bucket', 'channel_grades_prefix', 'channel_grades_region', 'channel_grades_path', 'mtx_wallet_bucket', 'mtx_wallet_prefix', 'mtx_wallet_region', 'mtx_wallet_path', 'user_modification_history_bucket', 'user_modification_history_prefix', 'user_modification_history_region', 'user_modification_history_path', 'user_auth_change_history_bucket', 'user_auth_change_history_prefix', 'user_auth_change_history_region', 'user_auth_change_history_path', 'user_account_history_bucket', 'user_account_history_prefix', 'user_account_history_region', 'user_account_history_path']
2025-06-09 00:05:18,820 - INFO - Ejecutando query: log_usuarios_completo
2025-06-09 00:05:30,792 - INFO - Se encontraron 21396 registros para procesar
2025-06-09 00:05:30,792 - INFO - Eliminando carpeta existente para evitar duplicados: REPORTE_LOG_USUARIOS/2025/06/05
2025-06-09 00:05:30,959 - INFO - Resultados exportados a REPORTE_LOG_USUARIOS/2025/06/05/REPORTE_LOG_USUARIO_2025-06-05_000518.parquet
2025-06-09 00:05:30,978 - INFO - Muestra de datos procesados:
  USERHISTID            CREATEDON TIPODOCUMENTO DOCUMENTO       MSISDN  MSISDNB    BANKDOMAIN        CREATED_BY         USERID  ... TIPODOCUMENTOA TIPODOCUMENTOB DOCUMENTOB NUMDOCUMENTOB        REQUESTTYPE                                            OLDDATA                                            NEWDATA  USERIDOLD ACCOUNTIDOLD
0       UM.1  2025-06-05 00:30:16           DNI  ********  ***********     <NA>  FCOMPARTAMOS  ****************  *************  ...            DNI           <NA>   ********          <NA>  User Modification  {"notificationEndpointRequests":[{"notificatio...  {"notificationEndpointRequests":[{"notificatio...       None         None
1       UM.2  2025-06-05 20:11:38           DNI  ********  ***********     <NA>  FCOMPARTAMOS   ***************  *************  ...            DNI           <NA>   ********          <NA>  User Modification  {"notificationEndpointRequests":[{"notificatio...  {"notificationEndpointRequests":[{"notificatio...       None         None
2       UM.3  2025-06-05 13:31:28           DNI  ********  ***********     <NA>  FCOMPARTAMOS  ****************  *************  ...            DNI           <NA>   ********          <NA>  User Modification  {"profileDetails":[{"identifierValue":"5198252...  {"profileDetails":[{"identifierValue":"5198252...       None         None
3       UM.4  2025-06-05 16:28:18           DNI  75412808  51982613327     <NA>  FCOMPARTAMOS  2080172756458448  7098382784684  ...            DNI           <NA>   75412808          <NA>  User Modification  {"profileDetails":[{"identifierValue":"5198261...  {"profileDetails":[{"identifierValue":"5198261...       None         None
4       UM.5  2025-06-05 17:47:03           DNI  00064249  51918548042     <NA>  FCOMPARTAMOS  2080172756458448  7096165625822  ...            DNI           <NA>   00064249          <NA>  User Modification  {"profileDetails":[{"identifierValue":"5191854...  {"profileDetails":[{"identifierValue":"5191854...       None         None

[5 rows x 34 columns]
2025-06-09 00:05:30,979 - INFO - Ejecutando script de post-procesamiento: REPORTES/REPORTE_LOG_USUARIO/postprocesar_log_usuario.py 2025-06-05
2025-06-09 00:06:05,330 - INFO - Script de post-procesamiento ejecutado con éxito: REPORTES/REPORTE_LOG_USUARIO/postprocesar_log_usuario.py
2025-06-09 00:06:05,330 - INFO - Salida del script:
Tabla conv_perfil cargada: 796 registros
Iniciando post-procesamiento de LOG_USUARIO para fecha: 2025-06-05
Procesando archivo: REPORTE_LOG_USUARIOS/2025/06/05/REPORTE_LOG_USUARIO_2025-06-05_000518.parquet
Aplicando deduplicación a REPORTE_LOG_USUARIOS/2025/06/05/REPORTE_LOG_USUARIO_2025-06-05_000518.parquet
Deduplicación completada:
  - Registros originales: 21,396
  - Registros después de deduplicación: 17,305
  - Duplicados eliminados: 4,091
🔄 Aplicando procesamiento con filtros exactos...
📊 Registros antes de filtros exactos: 21,396
📊 Registros después de filtros mínimos: 21,378
📊 Registros filtrados: 18
✅ Procesamiento con filtros exactos exitoso: 5 archivos
  📁 LOGUSR-FCOMPARTAMOS-20250605000605.csv
  📁 LOGUSR--20250605000605.csv
  📁 LOGUSR-BNACION-20250605000605.csv
  📁 LOGUSR-CRANDES-20250605000605.csv
  📁 LOGUSR-CCUSCO-20250605000605.csv
✅ Procesamiento completo exitoso: 5 archivos generados
✅ Post-procesamiento completado exitosamente
📁 Archivos generados: 5

2025-06-09 00:06:05,355 - INFO - Proceso completado
